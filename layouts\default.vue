<template>
  <div ref="layout" class="layout" :style="layoutStyle">
    <client-only>
      <div v-if="showHeader" class="hy-nav">
        <!-- <hyAffix> -->
        <headerV3 :type="pageType"></headerV3>
        <!-- </hyAffix> -->
      </div>
      <div v-if="showHeader" style="min-height: 65px;">
      </div>
      <transition name="el-fade-in-linear">
        <!-- 加key子路由变更就会刷新。。。。 -->
        <nuxt v-if="isRouteActive && !isNoRefresh" :key="$route.path" />
        <nuxt v-if="isRouteActive && isNoRefresh" />
      </transition>
      <!-- <hyFooter v-if="showFooter"></hyFooter> -->
      <!-- <footerV3 v-if="showFooter"></footerV3> -->
      <el-popover
          v-if="$route.path.indexOf('lectureHall') != -1"
          popper-class="lecture-hall_item"
          placement="right"
          width="140"
          trigger="hover">
          <div v-for="(item,index) in lectureHallUrlList" :key="index" @click="toPage(item.url)" :class="[routeUrl.includes(item.url)?'active':'']">
            {{ item.name }}
          </div>
          <div slot="reference" class="three-level" @click.stop>
            <div class="three-level_item">
              <i class="el-icon-position"></i>
            </div>
          </div>
      </el-popover>
      <el-popover
          v-if="$route.path.indexOf('huYuanSchool') != -1"
          popper-class="lecture-hall_item"
          placement="right"
          width="140"
          trigger="hover">
          <div v-for="(item,index) in huYuanSchoolUrlList" :key="index" @click="toPage(item.url)" :class="[routeUrl.includes(item.url)?'active':'']">
            {{ item.name }}
          </div>
          <div slot="reference" class="three-level" @click.stop>
            <div class="three-level_item">
              <i class="el-icon-position"></i>
            </div>
          </div>
      </el-popover>

      <!--浮动面板-->
      <hyRightFloatTool v-if="showHeader"></hyRightFloatTool>
      <!-- 返回顶部 -->
      <el-backtop class="backtop-box" target=".layout" :bottom="220">
        <div class="backtop circle flex-center">
          <i class="icon-arrow-up iconfont"></i>
        </div>
      </el-backtop>
      <!-- 三总fix box -->
     <div v-if="thirdData && !noThird" class="match-intro-third">
      <i class="el-icon-close" @click="thirdData = null"></i>
      <img :src="thirdData.pictureUrl" alt="" @click="goIntroThird(thirdData.linkUrl)" />
      <div class="match-intro-third-modal" @click="goIntroThird(thirdData.linkUrl)"></div>
     </div>
    </client-only>
  </div>
</template>

<script>
export default {
  middleware ({ route, redirect }) {
    //指向内部 定义了当前页面下的中间件，只会对当前页面或内部生效 定义在内部 可以开发和 外部中间件不一样的业务
    if (route.path == "/laboratory")
      redirect("/laboratory/personal");
    if (route.path == "/userCenter")
      redirect("/userCenter/profile");
    if (route.path == "/personalSpace")
      redirect("/personalSpace/article");
    // if (route.path.indexOf('enterprise')>-1) redirect("/");
    if (route.path == "/epManagementCenter")
      redirect({ path: "/epManagementCenter/overview", query: route.query });
  },
  data () {
    return {
      noThird: true,
      thirdData: null,
      clickEvent:null,
      pageType: 1,//1首页 2大家讲堂
      curRoute: "",
      showHeader: true,
      showFooter: true,
      isRouteActive: true,
      routeUrl:[],
      chatScript: null,
      lectureHallUrlList:[{
        name:'大家讲堂',
        url:'/lectureHall'
      },{
        name:'直播',
        url:'/lectureHall/lectureRoom'
      },{
        name:'回放',
        url:'/lectureHall/playback'
      },{
        name:'企业专场',
        url:'/lectureHall/enterprise'
      }],
      huYuanSchoolUrlList:[{
        name:'指令者学堂',
        url:'/huYuanSchool'
      },{
        name:'ISCCA认证班',
        url:'/'
      },{
        name:'99系列套餐',
        url:'/'
      },{
        name:'讲师',
        url:'/'
      }],
      isNoRefresh: false,
      layoutStyle: ''
    };
  },
  watch: {
    "$route.path": {
      handler (path) {
        this.routeUrl = [path]
        let navList = ['/home', '/information', '/lectureHall'];
        for (let i = 0; i < navList.length; i++) {
          let reg = new RegExp(`${navList[i]}`, 'ig');
          if (reg.test(path)) {
            this.pageType = i + 1;
            reg = null;
            break;
          }
        }
        // console.log(path)
        // console.log("defalut视图监听路由：", path);
        this.curRoute = path;
        // 切换路由页面滚动回顶部
        if (this.$refs.layout) {
          this.$nextTick(() => {
            this.$refs.layout.scrollTo(0, 0);
          });
        }
        // 配置不需要显示页头或页脚的路由页面
        // 无导航路由,把路由添加进来即可
        const noHeaderRoute = [
          "/login",
          "/match/signup",
          "/match/intro",
          "/match/prizeCollect",
          "/epManagementCenter",
          "/payCallback",
          '/examFaceRecognition'
          // "/onlineEpManager"
        ];
        // 无页脚路由,把路由添加进来即可
        const noFooterRoute = [
          "/login",
          "/match/signup",
          "/match/activity",
          "/laboratory/personal",
          "/personalSpace",
          // "/certification",
          "/articleDetail",
          // "/match/review",
          "/epManagementCenter",
          "/payCallback",
          '/examFaceRecognition'
          // "/onlineEpManager"
        ];
        let showHeader = true;
        let showFooter = true;
        noHeaderRoute.forEach((item) => {
          if (path.indexOf(item) > -1) {
            showHeader = false;
          }
        });
        noFooterRoute.forEach((item) => {
          if (path.indexOf(item) > -1) {
            showFooter = false;
          }
        });
        this.showHeader = showHeader;
        this.showFooter = showFooter;

        const noRefreshRouter = [
          'onlineEpManager',
          'userCenter'
        ]

        let noRefresh = false
        noRefreshRouter.forEach(item => {
          if (path.indexOf(item) > -1) {
            noRefresh = true
          }
        })
        this.isNoRefresh = noRefresh

        const noThirdRouter = [
          '/match/introThird',
          '/login'
        ]
        let noThird = false
        noThirdRouter.forEach(item => {
          if (path.indexOf(item) > -1) {
            noThird = true
          }
        })
        this.noThird = noThird
      },
      immediate: true,
    },
    "$route.query": {
      handler () {
        // 路由页面参数变化滚动回顶部
        if (this.$refs.layout) {
          this.$nextTick(() => {
            this.$refs.layout.scrollTo(0, 0);
          });
        }
      },
    },
  },
  created() {
    // this.initChat();
    this.getHomeData()
    // 退出登录事件监听
    this.$bus(this).$on("logout", () => {
      // console.log("logout");
      // 未登录状态下也需要重定向登录的页面路由
      let redirectUrls = [
        "/releaseArticle",
        "/userCenter",
        "/userCenter/profile",
        "/userCenter/identity",
        "/userCenter/account",
        "/userCenter/collection",
        "/userCenter/footprint",
        "/userCenter/articleManage",
        "/userCenter/comment"
      ];
      redirectUrls.forEach((item) => {
        if (this.curRoute.indexOf(item) > -1) {
          this.$router.push("/login");
        }
        return;
      });
      // 默认刷新视图根据接口判断是否跳转登录
      this.isRouteActive = false;
      this.$nextTick(function () {
        this.isRouteActive = true;
      });
    });
  },
  mounted () {
    this.getConfig()
    // this.$message.warning('fg')
    document.body.style.overflowX = 'hidden';
    document.documentElement.style.overflow = 'hidden';
    window.addEventListener('resize', this.nuxtResize)
    this.$nextTick(() => { this.nuxtResize() })
    this.routeUrl = [this.$route.path]
  },
  beforeDestroy() {
    clearInterval(this.timer);
    document.body.removeChild(this.chatScript);
    this.chatScript = null;
    this.clickEvent = null;
  },
  methods: {
    // 跳转三总专题页
    goIntroThird(url) {
      window.open(url)
    },
    getConfig() {
      //获取比赛按钮配置信息
      this.$axios({
        method: "post",
        url: this.$api.homeResource,
        data: ["BANNER_ADVERT"]
      }).then((res) => {
        if (res.data.data.BANNER_ADVERT.length) {
          this.thirdData = res.data.data.BANNER_ADVERT[0]
        }
      })
    },
    initChat() {
      this.chatScript = document.createElement('script');
      this.chatScript.src = 'https://gapi.bmy114.com/static/js/chat-front.js';
      this.chatScript.type = 'text/javascript';
      this.chatScript.onload = () => {
        // 初始化客服系统
        ROXCHAT.init({
          ROXCHAT_URL: "https://gapi.bmy114.com/",
          ROXCHAT_KEFU_ID: "aA9NEFcyU1ljantFUHByQgBdN1hSJQVAPiENBylaAll4TnBIWk9eBmR3fGRZIy4pMSA/UnVmGhw0JQ==",
          ROXCHAT_BTN_TEXT: "Welcome",
          ROXCHAT_LANG: "cn",
          CHAT_STYLEID: "369"
        })
        this.timer = setInterval(() => {
          let chatMain = document.getElementById('layui-layer19911116');//聊天界面窗口
          let chatBox = document.getElementsByClassName('entrance-examples-box');//悬浮按钮窗口
          if (chatBox && chatBox.length > 0) {
            this.clickEvent=()=>{
              this.scaleChatBox(null, chatBox, chatMain,true)
            }
            chatBox[0].addEventListener('click', this.clickEvent)
            // 缩放聊天窗口
            this.scaleChatBox(null, chatBox, chatMain);
            clearInterval(this.timer);
            this.timer = null;
          }
        },500)
      }
      document.body.appendChild(this.chatScript);
    },
    // 缩放聊天窗口
    scaleChatBox(bodyWidth, entrances, chatMain, isOneActive = false) {
      bodyWidth = bodyWidth || document.body.clientWidth;
      chatMain = chatMain || document.getElementById('layui-layer19911116');
      entrances = entrances && entrances.length > 0 ? entrances : document.getElementsByClassName('entrance-examples-box');
      // 第一次点击事件后移除事件监听
      isOneActive && (
        entrances[0].removeEventListener('click', this.clickEvent),
        this.clickEvent=null
      );
      if (entrances && entrances.length > 0) {
        chatMain && (
          chatMain.style.transformOrigin = 'right bottom',
          chatMain.style.transform = `scale(${(bodyWidth / 1920)})`
        );
        entrances[0].style.zoom = (bodyWidth / 1920)
        if (entrances.length > 1) entrances.forEach((item, index) => {
          if (index == 0) {
            item.style.zoom = (bodyWidth / 1920)
          } else {
            item.style.display = 'none'
          }
        })
      }
    },
    async nuxtResize () {
      const nuxt = document.getElementById('__nuxt')
      const bodyWidth = document.body.clientWidth
      const entrance = document.getElementsByClassName('entrance-examples-box') // 比目鱼
      if (nuxt && bodyWidth > 1920) {
        // nuxt.style.width = 1920 + 'px'
        this.layoutStyle = 'width: ' + 1920 + 'px;height: ' + 930 + 'px !important;'
        this.layoutStyle += 'zoom:' + (bodyWidth / 1920)
        // this.layoutStyle = `transformOrigin:top;transform:scale(${bodyWidth / 1920})`;
      } else {
        this.layoutStyle = ''
      }
      // console.log('nuxtResize', bodyWidth, entrance)
      // 缩放聊天窗口
      this.scaleChatBox(bodyWidth,entrance);
    },
    // 获取首页数据
    getHomeData () {
      this.$axios({
        method: "post",
        url: this.$api.homeResource,
        data: ["ARTICLE_TITLE", "BANNER", "STUDY_BANNER", "STATIC_ADVERT1", "STATIC_ADVERT2", "HOME_POP_ADVERT", "SERVICE_MARE", "PC_SIGN_BUTTON", "H5_SIGN_BUTTON", "FIRM_HOME_BANNER", "BACKGROUND", "FOREGROUND", "SLIDE_CAPTCHA", "LOGO", "BANNER_ADVERT"]
      }).then((res) => {
        //上面的判断到 index.html里面再根据 $store的 HomeImgData 处理
        this.$store.commit('changeHomeImgData', res?.data?.data)
      });
    },
    toPage (url) {
      if (url == '/') {
        return
      }
      this.routeUrl = [url]
      this.$router.push(url);
    },
  }
};
</script>
<style>
/* 用于处理顶部导航2级菜单自定义样式不生效的问题 */
.submenu-popupss {
  top: 70px !important;
}

.entrance-examples-box {
  width: max-content !important;
}
</style>
<style lang="scss" scoped>
.layout {
  scroll-behavior: smooth;
  // min-width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #f3f6fe;
  height: 100vh !important; // 返回顶部按钮需要设置，否则不生效
  overflow-x: auto;
  overflow-y: auto;
  // overflow: auto;

  // 滚动条整体样式
  &::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
    /* 纵向滚动条 宽度 */
    border-radius: 5px;
    /* 整体 圆角 */
  }

  //轨道部分
  &::-webkit-scrollbar-track {
    background: #fff !important;
  }

  // 滑块部分
  &::-webkit-scrollbar-thumb {
    background: #D4DBE0;
    min-height: 167px;
    width: 2px !important;
    border-radius: 5px;
  }

  scrollbar-width: thin !important;
  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
  -ms-overflow-style: none !important;
  /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */


  .backtop-box {
    height: 44px;
    width: 44px;
    transition: all 0.5s;
    cursor: pointer;

    &:hover {
      color: #006eff;
      font-weight: 900;
    }
  }

  .el-backtop {
    color: #9E9E9E;
    box-shadow: none;

  }

  .backtop {
    height: 100%;
    width: 100%;
    background-color: #fff;
    box-shadow: 0px 4px 30px #1A1B2B0D;
  }

}

.hy-nav {
  width: 100%;
  // z-index: 999;
  min-width: 1440px;
  position: fixed;
  top: 0px;
  z-index: 1001;
}
.three-level{
  position: absolute;
  top: 30%;
  left: 20px;
  height: 45px;
  width: 45px;
  border-radius: 50%;
  background: #006eff;
  z-index: 1000000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .three-level_item{
    position: fixed;
    font-size: 26px;
    color: #fff;
    i:hover {
      transform: rotate(90deg)
    }
  }
}
</style>
<style lang='scss'>
.lecture-hall_item{
  min-width: 100px !important;
  width: 140px !important;
  font-size: 16px;
  div{
    margin:6px 0;
    cursor: pointer;
  }
  div:hover {
    color: #0e76ff;
  }
  .active{
    color: #0e76ff;
    font-weight: 600;
  }
}
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}
@keyframes scale {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  90% {
    transform: scale(1);
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
.match-intro-third {
  width: 250px;
  height: 317px;
  position: fixed;
  bottom: 160px;
  right: 90px;
  overflow: hidden;
  border-radius: 4px;
  z-index: 999;
  border: 2px solid #ff7c35;
  box-shadow: 0px 0px 8px 1px #ff7c35;
  animation: bounce 2s infinite;
  .match-intro-third-modal {
    cursor: pointer;
    position: absolute;
    z-index: 999;
    width: 500px;
    height: 500px;
    top: -30%;
    left: -50%;
    background-color: #fff;
    border-radius: 50%;
    animation: scale 1.5s ease 0s infinite;
}
  &:hover {
    i {
      display: block;
    }
  }
  i {
    display: none;
    position: absolute;
    right: 2px;
    top: 2px;
    padding: 5px;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    z-index: 1000;
    &:hover {
      border-radius: 36px;
      background-color: rgba(255, 255, 255, 0.1)
    }
  }
  img {
    width: 100%;
    height: 100%;
    cursor: pointer;
    object-fit: fill;
  }
}
</style>
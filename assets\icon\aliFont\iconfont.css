@font-face {
  font-family: "iconfont"; /* Project id 3539432 */
  src: url('iconfont.woff2?t=1661941108037') format('woff2'),
       url('iconfont.woff?t=1661941108037') format('woff'),
       url('iconfont.ttf?t=1661941108037') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-dianzan:before {
  content: "\e8ad";
}

.icon-dianzan1:before {
  content: "\e8c3";
}

.icon-guolv:before {
  content: "\e64c";
}


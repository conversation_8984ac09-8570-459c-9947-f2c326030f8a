<template>
  <el-dialog class="enroll-dialog-wrap" title="报名查询" :visible.sync="dialogVisible" width="1200px" :before-close="dialogBeforeClose">
    <div class="enroll-dialog">
      <!-- 搜索区域 -->
      <div class="search-input mb-20">
        <el-input
          v-model="teamName"
          placeholder="战队/领队/队长"
          style="width: 320px;"
          @keyup.enter.native="searchData"
          suffix-icon="el-icon-search"
          clearable>
        </el-input>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        style="width: 100%;max-height: 600px;"
        height="400px"
        :header-cell-style="{ background: '#F4F6F7' }"
        stripe>
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column prop="teamName" label="战队名称" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="unitName" label="所属单位" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.unitName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="industry" label="所属行业" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.industry || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="region" label="地区（省/市）"show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ formatRegionPath(row.region) }}
          </template>
        </el-table-column>
        <el-table-column prop="leader" label="领队" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.leaderName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="captain" label="队长" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.captainName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="members" label="队员" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">
            <div class="flex jc-between ai-center">
              <div class="ellipsis overflow-tooltip">
                <div class="ellipsis">{{ (row.teamUsers && row.teamUsers[0] && row.teamUsers[0].nickName) || "-" }}</div>
              </div>
              <CountPopover :list="row.teamUsers || []" :userName="'nickName'"  />
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <!-- <el-pagination
        class="mt-20"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination> -->
    </div>
  </el-dialog>
</template>

<script>
import CountPopover from '@/components/common/CountPopover/index';
export default {
  components: {
    CountPopover
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      teamName: '',
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  created() {
    this.getEnrollList()
  },
  methods: {
    dialogBeforeClose() {
      this.$emit('update:dialogVisible', false);
    },
    searchData() {
      this.currentPage = 1;
      this.getEnrollList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getEnrollList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getEnrollList();
    },
    async getEnrollList() {
      try {
        const res = await this.$request.competition.getTeamList({
          teamName: this.teamName,
          pageNum: 1,
          pageSize: 10000
        });
        if (res && res.data && res.data.data && res.data.data.data) {
          this.tableData = res.data.data.data;
        } else {
          this.tableData = [];
        }
      } catch (error) {
        console.error('获取报名列表失败:', error);
        this.tableData = [];
      }
    },
    // 递归处理地区数据，生成完整路径
    formatAreaPath(areaData, targetAreaId = null) {
      if (!areaData) return ''

      // 如果没有指定目标ID，返回当前节点名称
      if (!targetAreaId) {
        return areaData.areaName
      }

      // 如果当前节点就是目标节点，返回路径
      if (areaData.areaId === targetAreaId) {
        return areaData.areaName
      }

      // 递归查找子节点
      if (areaData.children && areaData.children.length > 0) {
        for (const child of areaData.children) {
          const childPath = this.formatAreaPath(child, targetAreaId)
          if (childPath) {
            // 如果在子节点中找到了目标，返回完整路径
            return `${areaData.areaName}/${childPath}`
          }
        }
      }

      return ''
    },

    // 格式化地区路径显示
    formatRegionPath(regionData) {
      if (!regionData) return '-'

      // 如果regionData是地区ID，需要从完整地区数据中查找
      if (typeof regionData === 'number') {
        const fullAreaData = this.getFullAreaData()
        return this.formatAreaPath(fullAreaData, regionData)
      }

      // 如果regionData就是地区对象，直接处理
      if (typeof regionData === 'object') {
        return this.buildPathFromObject(regionData)
      }

      return regionData
    },

    // 从地区对象构建路径
    buildPathFromObject(areaObj) {
      if (!areaObj) return ''

      let path = areaObj.areaName
      let current = areaObj

      // 如果有children，找到最深层的节点
      while (current.children && current.children.length > 0) {
        current = current.children[0] // 取第一个子节点作为示例
        path += `/${current.areaName}`
      }

      return path
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog {
    background: #fafafa;
  }
}
.flex {
  display: flex;
}
.jc-between {
  justify-content: space-between;
}
.ai-center {
  align-items: center;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.enroll-dialog-wrap{
  ::v-deep .el-dialog__title{
    font-size: 16px;
  }
  ::v-deep .el-dialog__body {
    padding-top: 0;
  }
  ::v-deep .el-input__inner {
    height: 30px;
    padding: 0 10px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    color: #000;
  }
  ::v-deep .el-table {
    border: none;
  }
  ::v-deep .el-table__header th {
    background-color: #E5F0FF;
    color: #000;
  }
  ::v-deep .el-table__empty-text{
    font-size: 14px;
    color: #999;
  }
}
.enroll-dialog {
  background: #fff;
  padding: 20px;
  padding-bottom: 0;
  border-radius: 10px;
  .search-input{
    height: 30px;
    text-align: right;
    ::v-deep .el-input__suffix{
      top: -4px;
    }
  }
  .mb-20 {
    margin-bottom: 20px;
  }
  .mt-20 {
    margin-top: 20px;
  }
}
</style>

import prefix from "@/service/prefix";

// 获取企业实验室id
function getCompanyUid() {
  return localStorage.getItem("companyUid");
}

// 比赛列表
export function getListApi(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/home/<USER>
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 创建大赛
export function addApi(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/addHomeMatch`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 创建大赛-获取平台时间
export function getMatchTime() {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/getTime`.getFullApiUrl(),
    data: { companyUid: getCompanyUid() },
    method: "post",
  });
}

// 赛事记录
export function matchRecordList(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/history/list`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 小赛列表
export function getSmallMatchList(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/getSmallMatchList`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 删除小赛
export function deleteSmallMatch(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/deleteSmallMatch`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 创建小赛
export function addSmallMatch(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/addSmallMatch`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 更新小赛
export function updateSmallMatch(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/updateSmallMatch`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 小赛详情
export function getSmallMatchInfo(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/getSmallMatchInfo`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题-试卷列表
export function theoryTestPaper(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/paper/list`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库列表
export function theoryBankList(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/bank/list`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
//据题库ID查询下面的各种题目数量
export function theoryQuestionNum(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/bank/getQuestionNum`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--添加
export function addTheoryBank(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/bank/addQuestionBank`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--删除
export function delTheoryBank(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/bank/deleteQuestionBank`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--据题库ID查询下面的各种题目数量
export function getTheoryBankNum(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/bank/getQuestionNum`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--获取题目详情信息
export function getQuestDetail(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/question/getQuestionById`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--下载导入理论题目模板
export function downloadTem(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/question/template`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--获取行业
export function getIndustry() {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitions/getDictList`.getFullApiUrl(),
    data: { type: "INDUSTRY", companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--导出题库下所有的题目列表
export function exportQuestion(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/question/exportQuestion`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--更新理论题目信息
export function updateTheoryQuestion(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/question/updateQuestion`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 理论题库--获取题库下所有的题目列表
export function getTheoryBankQuestion(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/theory/question/list`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// CTF题库--获取题库下所有的题目列表
export function getCTFBankList(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/ctf/question/list`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
// CTF题库--新增CTF题目
export function addCTFBank(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/ctf/question/addQuestion`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
// AWD题库--获取AWD题库列表
export function getAWDBankList(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/awd/question/list`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
// AWD题库--新增AWD题目
export function addAWDBankQuestion(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/awd/question/addQuestion`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
// AWD题库--修改AWD题目
export function updateAWDBankQuestion(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/awd/question/updateQuestion`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
// AWD题库--删除AWD题目
export function deleteAWDBankQuestion(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/awd/question/deleteQuestion`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
// AWD题库--获取AWD题目详情
export function getAWDBankQuestionDetail(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/awd/question/getQuestionById`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
// 云主机--获取云主机配置列表
export function getCloudTemplateList(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/t/cloud/template/page`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
//云主机 -- 查询资源群集列表 原平台 "/control/tcloud/inner/availability_zone/get"
export function getAvailabilityZoneList(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/t/cloud/availability/zone/get`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
//云主机 -- 查询镜像列表 原平台 "/control/tcloud/inner/available_images/get"
export function getAvailableimages(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/t/cloud/availableImages/get`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
//云主机 -- 查询系统盘类型列表 原平台 "/control/tcloud/inner/volume_types/get"
export function getAvailableVolumeTypes(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/t/cloud/volumeTypes/get`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
//云主机 -- 查询云主机类型 原平台 "/control/tcloud/inner/cloud_host/getCloudHostType"
export function getCloudHostType(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/t/cloud/cloudHost/get`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
//云主机 -- 查询三层网络网络列表 原平台 "/control/tcloud/inner/networks/getNetworks"
export function getNetworks(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/t/cloud/networks/get`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}
//云主机 -- 查询安全组列表 原平台 "/control/tcloud/inner/networks/getNetworks"
export function getSecurityGroup(params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/t/cloud/securityGroup/get`.getFullApiUrl(),
    data: { ...params, companyUid: getCompanyUid() },
    method: "post",
  });
}

// 比赛列表-new
export function getMatchInfo(data) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitionManage/getInfo`.getFullApiUrl(),
    data: data,
    method: "post",
  });
}

// 比赛列表-new
export function getAreaTree(data) {
  return this.$axios({
    url: `${prefix.huanyu_web}/competitionRegistration/getAreaTree`.getFullApiUrl(),
    data: data,
    method: "get",
  });
}

// 比赛列表-new
export function getTeamList(data) {
  return this.$axios({
    url: `${prefix.huanyu_web}/index/getTeamList`.getFullApiUrl(),
    data: data,
    method: "post",
  });
}

export function matchSignUp(data) {
  return this.$axios({
    url: `${prefix.huanyu_web}/match/signUp`.getFullApiUrl(),
    data: data,
    method: "post",
  });
}
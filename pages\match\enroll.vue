<template>
  <div class="certification-wrap">
    <div class="title-header">
      <div class="title-txt">
        <i class="el-icon-arrow-left title-txt-icon" @click="handleGoBack"></i>
        <div>认证报名</div>
      </div>
    </div>
    <div class="certification-signup">
      <el-form
        :model="form"
        :rules="dynamicRules"
        label-position="left"
        ref="formRef"
        class="custom-asterisk"
        label-width="120px"
      >
        <!-- 单位信息 -->
        <el-card style="margin-bottom: 15px;">
          <el-divider content-position="left">单位信息</el-divider>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="单位名称" prop="unitName">
                <el-input v-model="form.unitName" maxlength="64" placeholder="请输入" size="small"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属集团" prop="groupName">
                <el-input v-model="form.groupName" maxlength="64" placeholder="请输入" size="small"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="所属行业" prop="industryId">
                <el-select v-model="form.industryId" placeholder="请选择" style="width: 100%" size="small" @change="onIndustryChange">
                  <el-option v-for="item in industryList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地区（省/市）" prop="areaId">
                <el-cascader
                  popper-class="region-cascader"
                  size="small"
                  style="width: 100%"
                  v-model="areaIdArray"
                  :options="regionList"
                  placeholder="请选择"
                  @change="onAreaChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card v-for="(team, tIndex) in form.teamList" class="team-card" :key="tIndex">
          <el-divider content-position="left">战队信息{{ tIndex + 1 }}</el-divider>
          <!-- 战队列表 -->
          <template>
            <div :key="tIndex" class="team-section">
              <el-row :gutter="24">
                <el-col :span="12">
                  <!-- 战队头部 -->
                  <el-form-item label="战队名称" :prop="`teamList[${tIndex}].teamName`">
                    <el-input style="width: 100%;" v-model="team.teamName" maxlength="64" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <i v-if="form.teamList.length > 1"
                    class="el-icon-delete remove-team"
                    @click="removeTeam(tIndex)"/>
                </el-col>
              </el-row>
              <!-- 成员列表 -->
              <div
                v-for="(member, mIndex) in team.teamUserList"
                :key="mIndex"
                class="team-item mb-15"
                >
                <el-row :gutter="10" class="item-title">
                  战队成员{{ mIndex + 1 }}
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item
                      :label="`姓名`"
                      :prop="`teamList[${tIndex}].teamUserList[${mIndex}].realName`"
                    >
                      <el-input v-model="member.realName" maxlength="64" placeholder="请输入" size="small"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="`性别`"
                      :prop="`teamList[${tIndex}].teamUserList[${mIndex}].gender`"
                    >
                      <el-select style="width: 100%" size="small" v-model="member.gender" placeholder="请选择">
                        <el-option label="男" value="0">
                        </el-option>
                        <el-option label="女" value="1">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item
                      :label="`角色`"
                      :prop="`teamList[${tIndex}].teamUserList[${mIndex}].roleName`"
                    >
                      <el-select v-model="member.roleName" placeholder="请选择" multiple style="width: 100%" size="small">
                        <el-option v-for="item in roleList" :key="item" :label="item" :value="item">{{ item }}</el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="photo">
                    <el-form-item
                      :prop="`teamList[${tIndex}].teamUserList[${mIndex}].photoUrl`"
                      class="photo-item"
                    >
                      <span slot="label">
                        照片
                        <el-tooltip effect="dark" content="请上传选手证件照，支持格式 jpg/jpeg/png，大小不超过2M" placement="top">
                          <i class="el-icon-warning-outline"></i>
                        </el-tooltip>
                      </span>
                      <el-upload
                        class="avatar-uploader"
                        :show-file-list="false"
                        :on-success="(res, file) => handleAvatarSuccess(res, file, tIndex, mIndex)"
                        :on-change="handleAvatarChange"
                        :before-upload="beforeAvatarUpload">
                        <el-image v-if="member.photoUrl" fit="contain" :src="member.photoUrl" class="avatar"/>
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item
                      :label="`手机号`"
                      :prop="`teamList[${tIndex}].teamUserList[${mIndex}].phoneNumber`"
                    >
                      <el-input v-model="member.phoneNumber" maxlength="64" placeholder="请输入" size="small"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item
                      :label="`身份证`"
                      :prop="`teamList[${tIndex}].teamUserList[${mIndex}].idCard`"
                    >
                      <el-input v-model="member.idCard" maxlength="64" placeholder="请输入" size="small"/>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item
                      :prop="`teamList[${tIndex}].teamUserList[${mIndex}].email`"
                    >
                      <div slot="label" style="text-indent: 4px;">邮箱</div>
                      <el-input v-model="member.email" maxlength="64" placeholder="请输入" size="small"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <i v-if="team.teamUserList.length > 1"
                  class="el-icon-delete remove-item"
                  @click="removeMember(tIndex, mIndex)"
                />
              </div>
              <el-button
                type="text"
                icon="el-icon-plus"
                :disabled="team.teamUserList.length >= 4"
                @click="addMember(tIndex)"
                >添加成员</el-button
              >
            </div>
          </template>
        </el-card>
        <el-button
          type="text"
          icon="el-icon-plus"
          @click="addTeam"
          >添加战队</el-button
        >
        <el-card>
          <!-- 备注 -->
          <el-row>
            <el-col :span="24">
              <el-form-item style="margin-right: 0 !important;" label="备注" prop="remarks">
                <el-input
                  type="textarea"
                  v-model="form.remarks"
                  placeholder="请输入"
                  rows="3"
                  maxlength="255"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
    </div>
    <div class="footer">
      <div class="footer-content">
        <div>
          <el-button type="text" @click="handleGoBack">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  // components: { Uploader },
  data() {
    return {
      submitLoading: false,
      form: {
        unitName: "",
        groupName: "",
        industryId: "",
        industryName: "",
        areaId: null,
        remarks: "",
        teamList: [
          {
            teamName: "",
            teamUserList: [
              {
                realName: "",
                roleName: [],
                phoneNumber: "",
                email: "",
                photoUrl: "",
                gender: "",
                idCard: "",
              },
            ],
          },
        ],
      },
      rules: {},
      industryList: [],
      regionList: [],
      roleList: ["领队", "队长", "队员"],
      areaIdArray: [], // 用于级联选择器的数组
    };
  },
  computed: {
    // 动态生成表单验证规则
    dynamicRules() {
      const rules = {
        "unitName": [{ required: true, message: "必填项" }],
        "industryId": [{ required: true, message: "必填项" }],
      };

      // 为每个战队和成员动态添加验证规则
      this.form.teamList.forEach((team, tIndex) => {
        rules[`teamList[${tIndex}].teamName`] = [
          { required: true, message: "必填项" },
          { validator: this.validateTeamName, trigger: 'blur' }
        ];

        team.teamUserList.forEach((member, mIndex) => {
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].realName`] = [{ required: true, message: "必填项" }];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].roleName`] = [
            { required: true, message: "必填项" },
            { validator: this.validateRole, trigger: 'change' }
          ];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].phoneNumber`] = [
            { required: true, message: "必填项" },
            { validator: this.validatePhone, trigger: 'blur' }
          ];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].idCard`] = [
            { required: true, message: "必填项" },
            { validator: this.validateIdCard, trigger: 'blur' }
          ];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].email`] = [
            { validator: this.validateEmail, trigger: 'blur' }
          ];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].gender`] = [{ required: true, message: "必填项" }];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].photoUrl`] = [{ required: true, message: "必填项" }];
        });
      });

      return rules;
    }
  },
  async created() {
    this.getIndustry()
    this.initAreaTree()
  },
  methods: {
    handleGoBack() {
      this.$router.go(-1)
    },
    // 验证战队名称是否重复
    validateTeamName(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }
      const teamNames = this.form.teamList.map(team => team.teamName).filter(name => name);
      const duplicateCount = teamNames.filter(name => name === value).length;
      if (duplicateCount > 1) {
        callback(new Error('已存在相同的战队名称'));
      } else {
        callback();
      }
    },

    // 验证角色是否重复
    validateRole(rule, value, callback) {
      if (!value || value.length === 0) {
        callback();
        return;
      }

      // 获取当前战队索引
      const propPath = rule.field;
      const teamIndex = parseInt(propPath.match(/teamList\[(\d+)\]/)[1]);
      const currentTeam = this.form.teamList[teamIndex];

      // 检查领队和队长是否重复
      const leaders = [];
      const captains = [];

      currentTeam.teamUserList.forEach(member => {
        if (member.roleName && member.roleName.includes('领队')) {
          leaders.push(member);
        }
        if (member.roleName && member.roleName.includes('队长')) {
          captains.push(member);
        }
      });

      if (leaders.length > 1) {
        callback(new Error('存在重复的领队'));
      } else if (captains.length > 1) {
        callback(new Error('存在重复的队长'));
      } else {
        callback();
      }
    },

    // 验证邮箱格式
    validateEmail(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        callback(new Error('邮箱格式错误'));
      } else {
        callback();
      }
    },

    // 验证电话格式
    validatePhone(rule, value, callback) {
      if (!value) {
        callback(new Error('必填项'));
        return;
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('电话格式错误'));
      } else {
        callback();
      }
    },

    // 验证身份证格式
    validateIdCard(rule, value, callback) {
      if (!value) {
        callback(new Error('必填项'));
        return;
      }
      // 身份证号码正则表达式（18位）
      const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
      if (!idCardRegex.test(value)) {
        callback(new Error('身份证格式错误'));
      } else {
        callback();
      }
    },
    async initAreaTree() {
      const result = await this.$request.competition.matchGetAreaTree({})
      if (result.data.code == 200) {
        this.regionList = result.data.data
        this.regionList.forEach(provinceItem => {
          provinceItem.label = provinceItem.areaName
          provinceItem.value = provinceItem.areaId
          provinceItem.children && provinceItem.children.length > 0 ? provinceItem.children.forEach(cityItem => {
            cityItem.label = cityItem.areaName
            cityItem.value = cityItem.areaId
            cityItem.children && cityItem.children.length > 0 ? cityItem.children.forEach(regionItem => {
              regionItem.label = regionItem.areaName
              regionItem.value = regionItem.areaId
            }) : ''
          }) : ''
        })
      }
    },
    getIndustry() {
      let formData = new FormData()
      formData.append("dictType", "hy_industry")
      this.$request.competition.getListByDictType(formData).then((res) => {
        if (res.data.code == 200) {
          this.industryList = res.data.data.list;
        }
      })
    },
    handleAvatarSuccess(res, file, teamIndex, memberIndex) {
      // 先显示本地预览图片
      // const imageUrl = URL.createObjectURL(file.raw);
      // this.form.teamList[teamIndex].teamUserList[memberIndex].photoUrl = imageUrl;

      // 上传到服务器
      let formData = new FormData();
      formData.append("file", file.raw);
      formData.append("web", "/blog/web");
      formData.append("sortName", "web");
      formData.append("projectName", "blog");

      this.$request.applicationApi.trainVerifySignUpUpFile(formData).then((uploadRes) => {
        if (uploadRes.data.code == this.$code.SUCCESS) {
          const data = uploadRes.data.data[0];
          // 上传成功后，替换为服务器返回的真实URL
          this.form.teamList[teamIndex].teamUserList[memberIndex].photoUrl = data.picCompleteUrl;
          this.$message.success('照片上传成功');
        } else {
          this.$message.error('照片上传失败');
          // 上传失败，清除预览图片
          this.form.teamList[teamIndex].teamUserList[memberIndex].photoUrl = '';
        }
      }).catch((error) => {
        console.error('照片上传错误:', error);
        this.$message.error('照片上传失败');
        // 上传失败，清除预览图片
        this.form.teamList[teamIndex].teamUserList[memberIndex].photoUrl = '';
      });
    },

    // 处理文件选择变化（用于预览）
    handleAvatarChange(file, fileList) {
      // 这个方法可以用来处理文件选择变化时的逻辑
      console.log('文件选择变化:', file);
    },
    beforeAvatarUpload(file) {
      const isJPEG = file.type === 'image/jpeg';
      const isJPG = file.type === 'image/jpg';
      const isPNG = file.type === 'image/png';
      const isGIF = file.type === 'image/gif';
      const isWebP = file.type === 'image/webp';
      const isValidFormat = isJPEG || isJPG || isPNG || isGIF || isWebP;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isValidFormat) {
        this.$message.error('上传照片只能是 JPG/JPEG/PNG/GIF/WebP 格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传照片大小不能超过 2MB!');
        return false;
      }

      return true;
    },
    // 战队操作
    addTeam() {
      this.form.teamList.push({
        teamName: "",
        teamUserList: [this.getDefaultMember()],
      });
      // 清除表单验证状态，避免新添加的字段立即显示验证错误
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      });
    },
    removeTeam(index) {
      this.form.teamList.splice(index, 1);
      // 清除表单验证状态
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      });
    },
    // 成员操作
    addMember(teamIndex) {
      this.form.teamList[teamIndex].teamUserList.push(this.getDefaultMember());
      // 清除表单验证状态，避免新添加的字段立即显示验证错误
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      });
    },
    removeMember(teamIndex, memberIndex) {
      this.form.teamList[teamIndex].teamUserList.splice(memberIndex, 1);
      // 清除表单验证状态
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      });
    },
    getDefaultMember() {
      return {
        realName: "",
        roleName: [],
        phoneNumber: "",
        email: "",
        photoUrl: "",
        gender: "",
        idCard: "",
      };
    },
    // 提交
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        const payload = {
          ...this.form,
          industryName: this.getIndustryName(this.form.industryId), // 添加行业名称
        };
        if (valid) {
          this.submitLoading = true
          this.submitData(payload);
        }
      });
    },

    // 获取行业名称
    getIndustryName(industryId) {
      const industry = this.industryList.find(item => item.dictValue === industryId);
      return industry ? industry.dictLabel : "";
    },

    // 行业变化处理
    onIndustryChange(value) {
      this.form.industryName = this.getIndustryName(value);
    },

    // 地区变化处理
    onAreaChange(value) {
      this.form.areaId = value && value.length > 0 ? value[value.length - 1] : null;
    },

    // 提交数据
    submitData(payload, isCovered = 0) {
      if (isCovered) {
        payload.isCovered = 1;
      }

      this.$request.competition.matchSignUp(payload).then((res) => {
        if (res.data.code === 200) {
          this.$message.success('报名成功');
          this.submitLoading = false
          // 可以跳转到成功页面或其他操作
        } else if (res.data.message && res.data.message.includes('检测到已有报名信息')) {
          // 显示确认对话框
          this.$confirm('检测到已有报名信息，重复报名会覆盖之前的报名数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 用户点击确定，重新提交并加上isCovered参数
            this.submitData(payload, 1);
          }).catch(() => {
            // 用户点击取消
          });
        } else {
        }
      }).catch((error) => {
        console.error('提交失败:', error);
        if (error.data.message && error.data.message.includes('检测到已有报名信息')) {
          // 显示确认对话框
          this.$confirm('检测到已有报名信息，重复报名会覆盖之前的报名数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 用户点击确定，重新提交并加上isCovered参数
            this.submitData(payload, 1);
          }).catch(() => {
            // 用户点击取消
          });
        }
        this.submitLoading = false
      });
    },
  },
};
</script>
<style lang="scss">
.region-cascader {
  .el-cascader-panel{
    height: 400px !important;
  }
}
</style>
<style scoped lang="scss">
::-webkit-scrollbar{
  width:7px;
  height:7px;
}
::-webkit-scrollbar-thumb {
  background: #c7d1da;
  border-radius: 3.5px;
}
::-webkit-scrollbar-track-piece {
  background: #efefef;
}
.certification-wrap {
  height: 100%;
  display: flex;
  min-width: 880px;
  overflow-y: hidden;
  flex-direction: column;
}
.title-header {
  background-color: #fff;
  border-bottom: 1px solid #dbdde0;
  width: 100%;
  height: 49px;
  line-height: 48px;
  padding: 0 24px;
  .title-txt{
    display: flex;
    align-items: center;
    font-size: 16px;
    height: 49px;
    line-height: 48px;
    font-weight: 900;
    margin: 0 auto;
    width: 65%;
    min-width: 830px;
    .title-txt-icon{
      font-size: 14px;
      margin-right: 5px;
      cursor: pointer;
    }
  }
}
.footer{
  width: 100%;
  padding: 0 24px;
  border-top: 1px solid #dbdde0;
  background-color: #fff;
  .footer-content{
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0 auto;
    width: 65%;
    min-width: 832px;
    max-width: 1200px;
  }
  .el-button--text{
    padding: 4px 15px;
  }
}
.certification-signup {
  width:1200px;
  margin: 20px auto;
  background-color: #F3f6FE;
  overflow-y: scroll;
  /deep/ .el-card__body {
    padding: 15px;
  }
  /deep/ .el-form-item {
    margin-right: 15px; // 同列内表单项的右侧间隔
    margin-bottom: 15px; // 表单项之间的间隔
  }
  /deep/ .team-card {
    margin-bottom: 15px;
    &:last-child {
      margin-bottom: 0 !important;
    }
  }
  .team-section {
    position: relative;
    border-radius: 2px;
    background-color: #fff;
    .dvd{
      ::v-deep .el-divider--horizontal{
        margin-top: 0;
      }
    }
    .team-header {
      width: 100%;
    }
    .remove-team{
      position: absolute;
      right: 4px;
      top: 10px;
      cursor: pointer;
      font-size: 16px;
    }
    .team-item {
      position: relative;
      background: #f3f6fe;
      padding: 5px;
      &:last-child{
        margin-bottom: 0;
      }
      .item-title{
        font-size: 14px;
        border-left: 2px solid #409EFF;
        padding-left: 10px;
      }
      .photo {
        position: relative;
        .photo-item {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }
      .remove-item {
        position: absolute;
        top: 5px;
        right: 5px;
        cursor: pointer;
      }
    }
  }
  .btn-group {
    display: flex;
    justify-content: flex-end;
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:before {
  content: '' !important;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:after {
  content: '*';
  color: #F56C6C;
  margin-left: 4px;
}
</style>

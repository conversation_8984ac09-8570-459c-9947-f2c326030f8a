<template>
  <div class="certification-signup">
    <div class="title-txt">认证报名</div>
    <el-form
      :model="form"
      :rules="dynamicRules"
      label-position="left"
      ref="formRef"
      class="custom-asterisk"
      label-width="120px"
    >
      <!-- 单位信息 -->
      <el-card style="margin-bottom: 20px;">
        <el-divider content-position="left">单位信息</el-divider>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="单位名称" prop="unitName">
              <el-input v-model="form.unitName" placeholder="请输入" size="small"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属集团" prop="groupName">
              <el-input v-model="form.groupName" placeholder="请输入" size="small"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="所属行业" prop="industryId">
              <el-select v-model="form.industryId" placeholder="请选择" style="width: 100%" size="small" @change="onIndustryChange">
                <el-option v-for="item in industryList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地区（省市）" prop="areaId">
              <el-cascader
                size="small"
                style="width: 100%"
                v-model="areaIdArray"
                :options="regionList"
                :props="{ label: 'name', value: 'code' }"
                placeholder="请选择"
                @change="onAreaChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card v-for="(team, tIndex) in form.teamList" class="mb-20">
        <el-divider content-position="left">战队信息{{ tIndex + 1 }}</el-divider>
        <!-- 战队列表 -->
        <template>
          <div :key="tIndex" class="team-section">
            <el-row :gutter="24">
              <el-col :span="12">
                <!-- 战队头部 -->
                <el-form-item label="战队名称" :prop="`teamList[${tIndex}].teamName`">
                  <el-input style="width: 100%;" v-model="team.teamName" placeholder="请输入" size="small"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <i v-if="tIndex > 0"
                  class="el-icon-delete remove-team"
                  @click="removeTeam(tIndex)"/>
              </el-col>
            </el-row>
            <el-divider/>
            <!-- 成员列表 -->
            <div
              v-for="(member, mIndex) in team.teamUserList"
              :key="mIndex"
              class="team-item mb-20"
              >
              <el-row :gutter="10" class="item-title">
                战队成员{{ mIndex + 1 }}
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`姓名`"
                    :prop="`teamList[${tIndex}].teamUserList[${mIndex}].realName`"
                  >
                    <el-input v-model="member.realName" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="`性别`"
                    :prop="`teamList[${tIndex}].teamUserList[${mIndex}].gender`"
                  >
                    <el-select style="width: 100%" size="small" v-model="member.gender" placeholder="请选择">
                      <el-option label="男" value="男">
                      </el-option>
                      <el-option label="女" value="女">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`角色`"
                    :prop="`teamList[${tIndex}].teamUserList[${mIndex}].roleName`"
                  >
                    <el-select v-model="member.roleName" placeholder="请选择" multiple style="width: 100%" size="small">
                      <el-option v-for="item in roleList" :key="item" :label="item" :value="item">{{ item }}</el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="photo">
                  <el-form-item
                    :prop="`teamList[${tIndex}].teamUserList[${mIndex}].photoUrl`"
                    class="photo-item"
                  >
                    <span slot="label">
                      照片
                      <el-tooltip effect="dark" content="请上传选手证件照，支持格式 jpg/jpeg/png，大小不超过2M" placement="top">
                        <i class="el-icon-warning-outline"></i>
                      </el-tooltip>
                    </span>
                    <el-upload
                      class="avatar-uploader"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :show-file-list="false"
                      :on-success="(res, file) => handleAvatarSuccess(res, file, tIndex, mIndex)"
                      :on-change="handleAvatarChange"
                      :before-upload="beforeAvatarUpload">
                      <img v-if="member.photoUrl" :src="member.photoUrl" class="avatar">
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`手机号`"
                    :prop="`teamList[${tIndex}].teamUserList[${mIndex}].phoneNumber`"
                  >
                    <el-input v-model="member.phoneNumber" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`身份证`"
                    :prop="`teamList[${tIndex}].teamUserList[${mIndex}].idCard`"
                  >
                    <el-input v-model="member.idCard" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`邮箱`"
                    :prop="`teamList[${tIndex}].teamUserList[${mIndex}].email`"
                  >
                    <el-input v-model="member.email" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <i v-if="mIndex > 0"
                class="el-icon-delete remove-item"
                @click="removeMember(tIndex, mIndex)"
              />
            </div>
            <el-button
              type="text"
              icon="el-icon-plus"
              :disabled="team.teamUserList.length >= 4"
              @click="addMember(tIndex)"
              >添加成员</el-button
            >
          </div>
        </template>
      </el-card>
      <el-card>
        <el-button
          type="text"
          icon="el-icon-plus"
          @click="addTeam"
          >添加战队</el-button
        >
        <!-- 备注 -->
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                type="textarea"
                v-model="form.remarks"
                placeholder="请输入"
                rows="3"
                maxlength="255"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 操作按钮 -->
        <div class="btn-group">
          <el-button type="primary" @click="submitForm">提交</el-button>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script>
// import Uploader from "@/components/Uploader"; // 自定义上传组件（封装文件选择+剪裁）
// import { getIndustryList, getRegionList } from "@/api/common";

export default {
  // components: { Uploader },
  data() {
    return {
      form: {
        unitName: "",
        groupName: "",
        industryId: "",
        industryName: "",
        areaId: null,
        remarks: "",
        teamList: [
          {
            teamName: "",
            teamUserList: [
              {
                realName: "",
                roleName: [],
                phoneNumber: "",
                email: "",
                photoUrl: "",
                gender: "",
              },
            ],
          },
        ],
      },
      rules: {},
      industryList: [],
      regionList: [],
      roleList: ["领队", "队长", "队员"],
      areaIdArray: [], // 用于级联选择器的数组
    };
  },
  computed: {
    // 动态生成表单验证规则
    dynamicRules() {
      const rules = {
        "unitName": [{ required: true, message: "必填项" }],
        "industryId": [{ required: true, message: "必填项" }],
      };

      // 为每个战队和成员动态添加验证规则
      this.form.teamList.forEach((team, tIndex) => {
        rules[`teamList[${tIndex}].teamName`] = [
          { required: true, message: "必填项" },
          { validator: this.validateTeamName, trigger: 'blur' }
        ];

        team.teamUserList.forEach((member, mIndex) => {
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].realName`] = [{ required: true, message: "必填项" }];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].roleName`] = [
            { required: true, message: "必填项" },
            { validator: this.validateRole, trigger: 'change' }
          ];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].phoneNumber`] = [
            { required: true, message: "必填项" },
            { validator: this.validatePhone, trigger: 'blur' }
          ];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].email`] = [
            { validator: this.validateEmail, trigger: 'blur' }
          ];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].gender`] = [{ required: true, message: "必填项" }];
          rules[`teamList[${tIndex}].teamUserList[${mIndex}].photoUrl`] = [{ required: true, message: "必填项" }];
        });
      });

      return rules;
    }
  },
  async created() {
    this.getIndustry()
    this.initAreaTree()
    // this.industryList = await getIndustryList();
    // this.regionList = await getRegionList();
    // const draft = await this.$storage.get("teamFormDraft");
    // if (draft) this.form = draft;
  },
  methods: {
    // 验证战队名称是否重复
    validateTeamName(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }
      const teamNames = this.form.teamList.map(team => team.teamName).filter(name => name);
      const duplicateCount = teamNames.filter(name => name === value).length;
      if (duplicateCount > 1) {
        callback(new Error('已存在相同的战队名称'));
      } else {
        callback();
      }
    },

    // 验证角色是否重复
    validateRole(rule, value, callback) {
      if (!value || value.length === 0) {
        callback();
        return;
      }

      // 获取当前战队索引
      const propPath = rule.field;
      const teamIndex = parseInt(propPath.match(/teamList\[(\d+)\]/)[1]);
      const currentTeam = this.form.teamList[teamIndex];

      // 检查领队和队长是否重复
      const leaders = [];
      const captains = [];

      currentTeam.teamUserList.forEach(member => {
        if (member.roleName && member.roleName.includes('领队')) {
          leaders.push(member);
        }
        if (member.roleName && member.roleName.includes('队长')) {
          captains.push(member);
        }
      });

      if (leaders.length > 1) {
        callback(new Error('存在重复的领队'));
      } else if (captains.length > 1) {
        callback(new Error('存在重复的队长'));
      } else {
        callback();
      }
    },

    // 验证邮箱格式
    validateEmail(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        callback(new Error('邮箱格式错误'));
      } else {
        callback();
      }
    },

    // 验证电话格式
    validatePhone(rule, value, callback) {
      if (!value) {
        callback(new Error('必填项'));
        return;
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('电话格式错误'));
      } else {
        callback();
      }
    },
    async initAreaTree() {
      const result = await this.$request.competition.matchGetAreaTree()
      if (result.code == this.$ECode.SUCCESS) {
        this.areaIdArray = result.data
        this.areaIdArray.forEach(provinceItem => {
          provinceItem.label = provinceItem.areaName
          provinceItem.value = provinceItem.areaId
          provinceItem.children && provinceItem.children.length > 0 ? provinceItem.children.forEach(cityItem => {
            cityItem.label = cityItem.areaName
            cityItem.value = cityItem.areaId
            cityItem.children && cityItem.children.length > 0 ? cityItem.children.forEach(regionItem => {
              regionItem.label = regionItem.areaName
              regionItem.value = regionItem.areaId
            }) : ''
          }) : ''
        })
      }
    },
    getIndustry() {
      this.$request.competition.getListByDictType({dictType:'hy_industry'}).then((res) => {
        if (res.data.code == 200) {
          this.industryList = res.data.data.list;
        }
      })
    },
    handleAvatarSuccess(res, file, teamIndex, memberIndex) {
      // 这里应该处理实际的文件上传，现在先用本地URL
      const imageUrl = URL.createObjectURL(file.raw);
      this.form.teamList[teamIndex].teamUserList[memberIndex].photoUrl = imageUrl;
      // 实际项目中应该上传到服务器并获取真实的URL
      let formData = new FormData();
      formData.append("file", file);
      formData.append("web", "/blog/web");
      formData.append("sortName", "web");
      formData.append("projectName", "blog");
      this.$request.applicationApi.trainVerifySignUpUpFile(formData).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
            const data = res.data.data[0];
            this.form.personalPhotoName = data.picName;
            this.form.personalPhotoUrl = data.picCompleteUrl;
            this.SignUpForm.personalPhotoFileList = res.data.data
            this.SignUpForm.personalPhotoFileList.map((item)=>{
              item.type = 1
            })
          }
      })

    },
    beforeAvatarUpload(file) {
      const isJPEG = file.type === 'image/jpeg';
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG&&!isJPEG&&!isPNG) {
        this.$message.error('上传头像图片只能是 jpg/jpeg/png 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    // 战队操作
    addTeam() {
      this.form.teamList.push({
        teamName: "",
        teamUserList: [this.getDefaultMember()],
      });
    },
    removeTeam(index) {
      this.form.teamList.splice(index, 1);
    },
    // 成员操作
    addMember(teamIndex) {
      this.form.teamList[teamIndex].teamUserList.push(this.getDefaultMember());
    },
    removeMember(teamIndex, memberIndex) {
      this.form.teamList[teamIndex].teamUserList.splice(memberIndex, 1);
    },
    getDefaultMember() {
      return {
        realName: "",
        roleName: [],
        phoneNumber: "",
        email: "",
        photoUrl: "",
        gender: "",
      };
    },
    // 提交
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          // 由于表单字段已经对应提交字段，直接使用form数据
          const payload = {
            ...this.form,
            industryName: this.getIndustryName(this.form.industryId), // 添加行业名称
          };

          this.submitData(payload);
        }
      });
    },

    // 获取行业名称
    getIndustryName(industryId) {
      const industry = this.industryList.find(item => item.dictValue === industryId);
      return industry ? industry.dictLabel : "";
    },

    // 行业变化处理
    onIndustryChange(value) {
      this.form.industryName = this.getIndustryName(value);
    },

    // 地区变化处理
    onAreaChange(value) {
      this.form.areaId = value && value.length > 0 ? value[value.length - 1] : null;
    },

    // 提交数据
    submitData(payload, isCovered = 0) {
      if (isCovered) {
        payload.isCovered = 1;
      }

      this.$request.competition.matchSignUp(payload).then((res) => {
        if (res.data.code === 200) {
          this.$message.success('报名成功');
          // 可以跳转到成功页面或其他操作
        } else if (res.data.message && res.data.message.includes('检测到已有报名信息')) {
          // 显示确认对话框
          this.$confirm('检测到已有报名信息，重复报名会覆盖之前的报名数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 用户点击确定，重新提交并加上isCovered参数
            this.submitData(payload, 1);
          }).catch(() => {
            // 用户点击取消
          });
        } else {
        }
      }).catch((error) => {
        console.error('提交失败:', error);
      });
    },
  },
};
</script>

<style scoped lang="scss">
.certification-signup {
  min-width:1440px;
  margin: 20px auto;
  padding: 20px;
  overflow-y: scroll;
  /deep/ .el-form-item {
    margin-right: 20px; // 同列内表单项的右侧间隔
  }
  .title-txt {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  .team-section {
    position: relative;
    border-radius: 2px;
    background-color: #fff;
    .team-header {
      width: 100%;
    }
    .remove-team{
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
      color: #f56c6c;
      font-size: 18px;
      &:hover {
        color: #f78989;
      }
    }
    .team-item {
      position: relative;
      background: #f3f6fe;
      padding: 5px;
      .item-title{
        font-size: 14px;
        border-left: 2px solid #409EFF;
        padding-left: 10px;
      }
      .photo {
        position: relative;
        .photo-item {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }
      .remove-item {
        position: absolute;
        top: 5px;
        right: 5px;
        cursor: pointer;
      }
    }
  }
  .btn-group {
    display: flex;
    justify-content: flex-end;
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:before {
  content: '' !important;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:after {
  content: '*';
  color: #F56C6C;
  margin-left: 4px;
}
</style>

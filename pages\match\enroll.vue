<template>
  <div class="certification-signup">
    <div class="title-txt">认证报名</div>
    <el-form
      :model="form"
      :rules="dynamicRules"
      label-position="left"
      ref="formRef"
      class="custom-asterisk"
      label-width="120px"
    >
      <!-- 单位信息 -->
      <el-card style="margin-bottom: 20px;">
        <el-divider content-position="left">单位信息</el-divider>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="单位名称" prop="unit.name">
              <el-input v-model="form.unit.name" placeholder="请输入" size="small"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属集团" prop="unit.group">
              <el-input v-model="form.unit.group" placeholder="请输入" size="small"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="所属行业" prop="unit.industry">
              <el-select v-model="form.unit.industry" placeholder="请选择" style="width: 100%" size="small">
                <el-option v-for="item in industryList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地区（省市）" prop="unit.region">
              <el-cascader
                size="small"
                style="width: 100%"
                v-model="form.unit.region"
                :options="regionList"
                :props="{ label: 'name', value: 'code' }"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card v-for="(team, tIndex) in form.teams" class="mb-20">
        <el-divider content-position="left">战队信息{{ tIndex + 1 }}</el-divider>
        <!-- 战队列表 -->
        <template>
          <div :key="tIndex" class="team-section">
            <el-row :gutter="24">
              <el-col :span="12">
                <!-- 战队头部 -->
                <el-form-item label="战队名称" :prop="`teams[${tIndex}].name`">
                  <el-input style="width: 100%;" v-model="team.name" placeholder="请输入" size="small"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <i v-if="tIndex > 0"
                  class="el-icon-delete remove-team"
                  @click="removeTeam(tIndex)"/>
              </el-col>
            </el-row>
            <el-divider/>
            <!-- 成员列表 -->
            <div
              v-for="(member, mIndex) in team.members"
              :key="mIndex"
              class="team-item mb-20"
              >
              <el-row :gutter="10" class="item-title">
                战队成员{{ mIndex + 1 }}
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`姓名`"
                    :prop="`teams[${tIndex}].members[${mIndex}].name`"
                  >
                    <el-input v-model="member.name" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="`性别`"
                    :prop="`teams[${tIndex}].members[${mIndex}].gender`"
                  >
                    <el-select style="width: 100%" size="small" v-model="member.gender" placeholder="请选择">
                      <el-option label="男" value="男">
                      </el-option>
                      <el-option label="女" value="女">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`角色`"
                    :prop="`teams[${tIndex}].members[${mIndex}].role`"
                  >
                    <el-select v-model="member.role" placeholder="请选择" multiple style="width: 100%" size="small">
                      <el-option v-for="item in roleList" :key="item" :label="item" :value="item">{{ item }}</el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="photo">
                  <el-form-item
                    :prop="`teams[${tIndex}].members[${mIndex}].photo`"
                    class="photo-item"
                  >
                    <span slot="label">
                      照片<i class=""></i>
                    </span>
                    <el-upload
                      class="avatar-uploader"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :show-file-list="false"
                      :on-success="(res, file) => handleAvatarSuccess(res, file, tIndex, mIndex)"
                      :before-upload="beforeAvatarUpload">
                      <img v-if="member.photo" :src="member.photo" class="avatar">
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`手机号`"
                    :prop="`teams[${tIndex}].members[${mIndex}].phone`"
                  >
                    <el-input v-model="member.phone" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`邮箱`"
                    :prop="`teams[${tIndex}].members[${mIndex}].email`"
                  >
                    <el-input v-model="member.email" placeholder="请输入" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <i v-if="mIndex > 0"
                class="el-icon-delete remove-item"
                @click="removeMember(tIndex, mIndex)"
              />
            </div>
            <el-button
              type="text"
              icon="el-icon-plus"
              :disabled="team.members.length >= 4"
              @click="addMember(tIndex)"
              >添加成员</el-button
            >
          </div>
        </template>
      </el-card>
      <el-card>
        <el-button
          type="text"
          icon="el-icon-plus"
          @click="addTeam"
          >添加战队</el-button
        >
        <!-- 备注 -->
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="form.remark"
                placeholder="请输入"
                rows="3"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 操作按钮 -->
        <div class="btn-group">
          <el-button type="primary" @click="submitForm">提交</el-button>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script>
// import Uploader from "@/components/Uploader"; // 自定义上传组件（封装文件选择+剪裁）
// import { getIndustryList, getRegionList } from "@/api/common";

export default {
  // components: { Uploader },
  data() {
    return {
      form: {
        unit: {
          name: "",
          group: "",
          industry: "",
          region: [],
        },
        teams: [
          {
            name: "",
            members: [
              {
                name: "",
                role: [],
                phone: "",
                email: "",
                photo: "",
                photoName: "",
                gender: "",
              },
            ],
          },
        ],
        remark: "",
      },
      rules: {},
      industryList: [],
      regionList: [],
      roleList: ["领队", "队长", "队员"],
    };
  },
  computed: {
    // 动态生成表单验证规则
    dynamicRules() {
      const rules = {
        "unit.name": [{ required: true, message: "必填项" }],
        "unit.industry": [{ required: true, message: "必填项" }],
      };

      // 为每个战队和成员动态添加验证规则
      this.form.teams.forEach((team, tIndex) => {
        rules[`teams[${tIndex}].name`] = [
          { required: true, message: "必填项" },
          { validator: this.validateTeamName, trigger: 'blur' }
        ];

        team.members.forEach((member, mIndex) => {
          rules[`teams[${tIndex}].members[${mIndex}].name`] = [{ required: true, message: "必填项" }];
          rules[`teams[${tIndex}].members[${mIndex}].role`] = [
            { required: true, message: "必填项" },
            { validator: this.validateRole, trigger: 'change' }
          ];
          rules[`teams[${tIndex}].members[${mIndex}].phone`] = [
            { required: true, message: "必填项" },
            { validator: this.validatePhone, trigger: 'blur' }
          ];
          rules[`teams[${tIndex}].members[${mIndex}].email`] = [
            { validator: this.validateEmail, trigger: 'blur' }
          ];
          rules[`teams[${tIndex}].members[${mIndex}].gender`] = [{ required: true, message: "必填项" }];
          rules[`teams[${tIndex}].members[${mIndex}].photo`] = [{ required: true, message: "必填项" }];
        });
      });

      return rules;
    }
  },
  async created() {
    this.getIndustry()
    // this.industryList = await getIndustryList();
    // this.regionList = await getRegionList();
    // const draft = await this.$storage.get("teamFormDraft");
    // if (draft) this.form = draft;
  },
  methods: {
    // 验证战队名称是否重复
    validateTeamName(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }
      const teamNames = this.form.teams.map(team => team.name).filter(name => name);
      const duplicateCount = teamNames.filter(name => name === value).length;
      if (duplicateCount > 1) {
        callback(new Error('已存在相同的战队名称'));
      } else {
        callback();
      }
    },

    // 验证角色是否重复
    validateRole(rule, value, callback) {
      if (!value || value.length === 0) {
        callback();
        return;
      }

      // 获取当前战队索引
      const propPath = rule.field;
      const teamIndex = parseInt(propPath.match(/teams\[(\d+)\]/)[1]);
      const currentTeam = this.form.teams[teamIndex];

      // 检查领队和队长是否重复
      const leaders = [];
      const captains = [];

      currentTeam.members.forEach(member => {
        if (member.role && member.role.includes('领队')) {
          leaders.push(member);
        }
        if (member.role && member.role.includes('队长')) {
          captains.push(member);
        }
      });

      if (leaders.length > 1) {
        callback(new Error('存在重复的领队'));
      } else if (captains.length > 1) {
        callback(new Error('存在重复的队长'));
      } else {
        callback();
      }
    },

    // 验证邮箱格式
    validateEmail(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        callback(new Error('邮箱格式错误'));
      } else {
        callback();
      }
    },

    // 验证电话格式
    validatePhone(rule, value, callback) {
      if (!value) {
        callback(new Error('必填项'));
        return;
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('电话格式错误'));
      } else {
        callback();
      }
    },

    getIndustry() {
      this.$axios({
        method: "post",
        url: this.$api.sysDictDataGetListByDictType + '?dictType=event_industry',
        data: {}
      }).then((res) => {
        if (res.data.code == 200) {
          this.industryList = res.data.data.list;
        }
      })
    },
    handleAvatarSuccess(res, file, teamIndex, memberIndex) {
      // 这里应该处理实际的文件上传，现在先用本地URL
      const imageUrl = URL.createObjectURL(file.raw);
      this.form.teams[teamIndex].members[memberIndex].photo = imageUrl;
      // 实际项目中应该上传到服务器并获取真实的URL
      // this.form.teams[teamIndex].members[memberIndex].photo = res.data.url;
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    // 战队操作
    addTeam() {
      this.form.teams.push({
        name: "",
        members: [this.getDefaultMember()],
      });
    },
    removeTeam(index) {
      this.form.teams.splice(index, 1);
    },
    // 成员操作
    addMember(teamIndex) {
      this.form.teams[teamIndex].members.push(this.getDefaultMember());
    },
    removeMember(teamIndex, memberIndex) {
      this.form.teams[teamIndex].members.splice(memberIndex, 1);
    },
    getDefaultMember() {
      return {
        name: "",
        role: [],
        phone: "",
        email: "",
        photo: "",
        photoName: "",
        gender: "",
      };
    },
    // 提交
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          // 构造请求数据，按照API要求的格式
          const payload = {
            unitName: this.form.unit.name, // 必填，1-64位
            groupName: this.form.unit.group || "", // 非必填，1-64位
            areaId: this.form.unit.region && this.form.unit.region.length > 0 ? this.form.unit.region[this.form.unit.region.length - 1] : null, // 地区（省/市）非必填
            industryId: this.form.unit.industry, // 必填
            industryName: this.getIndustryName(this.form.unit.industry), // 行业名称
            remarks: this.form.remark || "", // 最多输入255个字符
            teamList: this.form.teams.map((team) => ({
              teamName: team.name, // 必填，1-64，不允许重复
              teamUserList: team.members.map((member) => ({
                realName: member.name, // 必填，1-64
                roleName: member.role, // 必填，多选：领队/队长/队员
                email: member.email || "", // 非必填，需要校验格式
                gender: member.gender, // 性别
                phoneNumber: member.phone, // 必填，需要校验格式
                photoUrl: member.photo || "" // 照片路径
              }))
            }))
          };

          this.submitData(payload);
        }
      });
    },

    // 获取行业名称
    getIndustryName(industryId) {
      const industry = this.industryList.find(item => item.value === industryId);
      return industry ? industry.label : "";
    },

    // 提交数据
    submitData(payload, isCovered = 0) {
      if (isCovered) {
        payload.isCovered = 1;
      }

      this.$request.competition.matchSignUp(payload).then((res) => {
        if (res.data.code === 200) {
          this.$message.success('报名成功');
          // 可以跳转到成功页面或其他操作
        } else if (res.data.message && res.data.message.includes('检测到已有报名信息')) {
          // 显示确认对话框
          this.$confirm('检测到已有报名信息，重复报名会覆盖之前的报名数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 用户点击确定，重新提交并加上isCovered参数
            this.submitData(payload, 1);
          }).catch(() => {
            // 用户点击取消
            this.$message.info('已取消报名');
          });
        } else {
          this.$message.error(res.data.message || '报名失败');
        }
      }).catch((error) => {
        console.error('提交失败:', error);
        this.$message.error('网络错误，请稍后重试');
      });
    },
  },
};
</script>

<style scoped lang="scss">
.certification-signup {
  min-width:1440px;
  margin: 20px auto;
  padding: 20px;
  overflow-y: scroll;
  /deep/ .el-form-item {
    margin-right: 20px; // 同列内表单项的右侧间隔
  }
  .title-txt {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  .team-section {
    position: relative;
    border-radius: 2px;
    background-color: #fff;
    .team-header {
      width: 100%;
    }
    .remove-team{
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
      color: #f56c6c;
      font-size: 18px;
      &:hover {
        color: #f78989;
      }
    }
    .team-item {
      position: relative;
      background: #f3f6fe;
      padding: 5px;
      .item-title{
        font-size: 14px;
        border-left: 2px solid #409EFF;
        padding-left: 10px;
      }
      .photo {
        position: relative;
        .photo-item {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }
      .remove-item {
        position: absolute;
        top: 5px;
        right: 5px;
        cursor: pointer;
      }
    }
  }
  .btn-group {
    display: flex;
    justify-content: flex-end;
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:before {
  content: '' !important;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:after {
  content: '*';
  color: #F56C6C;
  margin-left: 4px;
}
</style>

<template>
  <div class="certification-signup">
    <div class="title-txt">认证报名</div>
    <el-form
      :model="form"
      :rules="rules"
      label-position="left"
      ref="formRef"
      class="custom-asterisk"
      label-width="120px"
    >
      <!-- 单位信息 -->
      <el-card style="margin-bottom: 20px;">
        <el-divider content-position="left">单位信息</el-divider>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="单位名称" prop="unit.name">
              <el-input v-model="form.unit.name" size="small"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属集团" prop="unit.group">
              <el-input v-model="form.unit.group" size="small"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="所属行业" prop="unit.industry">
              <el-select v-model="form.unit.industry" :options="industryList" style="width: 100%" size="small"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地区（省市）" prop="unit.region">
              <el-cascader
                size="small"
                style="width: 100%"
                v-model="form.unit.region"
                :options="regionList"
                :props="{ label: 'name', value: 'code' }"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card v-for="(team, tIndex) in form.teams" class="mb-20">
        <el-divider content-position="left">战队信息{{ tIndex + 1 }}</el-divider>
        <!-- 战队列表 -->
        <template>
          <div :key="tIndex" class="team-section">
            <el-row :gutter="24">
              <el-col :span="12">
                <!-- 战队头部 -->
                <el-form-item label="战队名称" :prop="`teams[${tIndex}].name`">
                  <el-input style="width: 100%;" v-model="team.name" size="small"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <i v-if="tIndex > 0"
                  icon="el-icon-delete remove-team"
                  @click="removeTeam(tIndex)"/>
              </el-col>
            </el-row>
            <el-divider/>
            <!-- 成员列表 -->
            <div
              v-for="(member, mIndex) in team.members"
              :key="mIndex"
              class="team-item mb-20"
              >
              <el-row :gutter="10" class="item-title">
                战队成员{{ mIndex + 1 }}
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`姓名`"
                    :prop="`teams[${tIndex}].members[${mIndex}].name`"
                  >
                    <el-input v-model="member.name" size="small"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="`性别`"
                    :prop="`teams[${tIndex}].members[${mIndex}].gender`"
                  >
                    <el-select v-model="model" placeholder="请选择">
                      <el-option label="男" value="男">
                      </el-option>
                      <el-option label="女" value="女">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`角色`"
                    :prop="`teams[${tIndex}].members[${mIndex}].role`"
                  >
                    <el-select v-model="member.role" style="width: 100%" size="small">
                      <el-option v-for="item in roleList" :key="item" :label="item" :value="item">{{ item }}</el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="photo">
                  <el-form-item
                    :label="`照片`"
                    :prop="`teams[${tIndex}].members[${mIndex}].photo`"
                    class="photo-item"
                  >
                    <el-upload
                      class="avatar-uploader"
                      action="https://jsonplaceholder.typicode.com/posts/"
                      :show-file-list="false"
                      :on-success="handleAvatarSuccess"
                      :before-upload="beforeAvatarUpload">
                      <img v-if="member.photo" :src="member.photo" class="avatar">
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`手机号`"
                    :prop="`teams[${tIndex}].members[${mIndex}].phone`"
                  >
                    <el-input v-model="member.phone" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`身份证号`"
                    :prop="`teams[${tIndex}].members[${mIndex}].idCard`"
                  >
                    <el-input v-model="member.idCard" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    :label="`邮箱`"
                    :prop="`teams[${tIndex}].members[${mIndex}].email`"
                  >
                    <el-input v-model="member.email" size="small"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <i v-if="mIndex > 0"
                class="el-icon-delete remove-item"
                @click="removeMember(tIndex, mIndex)"
              />
            </div>
            <el-button
              type="text"
              icon="el-icon-plus"
              @click="addMember(tIndex)"
              >添加成员</el-button
            >
          </div>
        </template>
      </el-card>
      <el-card>
        <el-button
          type="text"
          icon="el-icon-plus"
          @click="addTeam"
          >添加战队</el-button
        >
        <!-- 备注 -->
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="form.remark"
                rows="3"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 操作按钮 -->
        <div class="btn-group">
          <el-button type="primary" @click="submitForm">提交</el-button>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script>
// import Uploader from "@/components/Uploader"; // 自定义上传组件（封装文件选择+剪裁）
// import { getIndustryList, getRegionList } from "@/api/common";

export default {
  // components: { Uploader },
  data() {
    return {
      form: {
        unit: {
          name: "",
          group: "",
          industry: "",
          region: [],
        },
        teams: [
          {
            name: "",
            members: [
              {
                name: "",
                role: "",
                phone: "",
                email: "",
                photo: "",
                photoName: "",
              },
            ],
          },
        ],
        remark: "",
      },
      rules: {
        "unit.name": [{ required: true, message: "必填项" }],
        "unit.industry": [{ required: true, message: "必填项" }],
        teams: [
          {
            name: [{ required: true, message: "必填项" }],
            members: [
              {
                name: [{ required: true, message: "必填项" }],
                role: [{ required: true, message: "必填项" }],
                phone: [{ required: true, message: "必填项" }],
                email: [{ type: "email", message: "邮箱格式错误" }],
                photo: [{ required: true, message: "必填项" }],
              },
            ],
          },
        ],
      },
      industryList: [],
      regionList: [],
      roleList: ["领队", "队长", "队员"],
    };
  },
  async created() {
    // this.industryList = await getIndustryList();
    // this.regionList = await getRegionList();
    // const draft = await this.$storage.get("teamFormDraft");
    // if (draft) this.form = draft;
  },
  methods: {
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    // 战队操作
    addTeam() {
      this.form.teams.push({
        name: "",
        members: [this.getDefaultMember()],
      });
    },
    removeTeam(index) {
      this.form.teams.splice(index, 1);
    },
    // 成员操作
    addMember(teamIndex) {
      this.form.teams[teamIndex].members.push(this.getDefaultMember());
    },
    removeMember(teamIndex, memberIndex) {
      this.form.teams[teamIndex].members.splice(memberIndex, 1);
    },
    getDefaultMember() {
      return {
        name: "",
        role: "",
        phone: "",
        email: "",
        photo: "",
        photoName: "",
      };
    },
    // 提交
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          // 构造请求数据
          const payload = {
            unit: this.form.unit,
            teams: this.form.teams.map((team) => ({
              name: team.name,
              members: team.members.map((m) => ({
                ...m,
                photo: m.photo, // 处理文件上传后的地址
              })),
            })),
            remark: this.form.remark,
          };
           this.$request.competition.matchSignup(payload).then((res) => {

           })
          // const res = await api.submitTeamForm(payload)
          // if(res.success) { ... }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.certification-signup {
  min-width:1440px;
  margin: 20px auto;
  padding: 20px;
  overflow-y: scroll;
  /deep/ .el-form-item {
    margin-right: 20px; // 同列内表单项的右侧间隔
  }
  .title-txt {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  .team-section {
    position: relative;
    border-radius: 2px;
    background-color: #fff;
    .team-header {
      width: 100%;
      .remove-team{
        position: absolute;
        right: 0;
        top: 0;
        cursor: pointer;
      }
    }
    .team-item {
      position: relative;
      background: #f3f6fe;
      padding: 5px;
      .item-title{
        font-size: 14px;
        border-left: 2px solid #409EFF;
        padding-left: 10px;
      }
      .photo {
        position: relative;
        .photo-item {
          position: absolute;
          top: 0;
          left: 0;
        }
      }
      .remove-item {
        position: absolute;
        top: 5px;
        right: 5px;
        cursor: pointer;
      }
    }
  }
  .btn-group {
    display: flex;
    justify-content: flex-end;
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:before {
  content: '' !important;
}

.custom-asterisk /deep/ .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label:after {
  content: '*';
  color: #F56C6C;
  margin-left: 4px;
}
</style>
